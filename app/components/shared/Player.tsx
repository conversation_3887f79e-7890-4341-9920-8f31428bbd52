import React, { useEffect, useRef, useState, useCallback, type Ref } from 'react';
import { Button } from '~/components/ui/button';
import type { Readable } from '~/lib/types';
import { useToast } from "~/components/ui/toast";

interface PlayerProps {
    isVisible: boolean;
    title: string;
    onClose: () => void;
    readable: Readable;
}

interface AudioChunk {
    text: string;
    src: string;
    id: number;
    index?: number;
    total?: number;
}

const Player: React.FC<PlayerProps> = ({
    isVisible,
    title,
    onClose,
    readable
}) => {
    if (!isVisible) return null;

    const worker = useRef<Worker | null>(null);
    const audioRef = useRef<HTMLAudioElement | null>(null);

    // State for player
    const [voices, setVoices] = useState<any[]>([]);
    const [status, setStatus] = useState<string>("");
    const [error, setError] = useState<string | null>(null);
    const [isPaused, setIsPaused] = useState<boolean>(false);

    // State for model loading
    const [isModelLoading, setIsModelLoading] = useState<boolean>(false);
    const [modelLoadingProgress, setModelLoadingProgress] = useState<{
        [key: string]: {
            name: string;
            file: string;
            progress: number;
            loaded: number;
            total: number;
            status: string;
        }
    }>({});
    const [modelLoadingComplete, setModelLoadingComplete] = useState<boolean>(false);

    // State for audio chunks
    const [audioChunks, setAudioChunks] = useState<AudioChunk[]>([]);
    const [currentChunkIndex, setCurrentChunkIndex] = useState<number>(-1);
    const [isPlaying, setIsPlaying] = useState<boolean>(false);

    const { showToast } = useToast();

    // Refs to track state between renders and prevent race conditions
    const audioChunksRef = useRef<AudioChunk[]>([]);
    const currentChunkIndexRef = useRef<number>(-1);
    const isPlayingRef = useRef<boolean>(false);
    const isPausedRef = useRef<boolean>(false);
    const processingChunkRef = useRef<boolean>(false);

    // Generate new audio
    const handleGenerate = useCallback(async () => {
        if (!worker.current) return;

        setStatus("running");

        const workercb = async (worker: any) => {
            if (readable.id) {
                const text = await fetch(`/readables/${readable.id}`);
                const textToGenerate = await text.json();
                worker.postMessage({
                    type: "stream",
                    text: textToGenerate.content.toString("utf-8"),
                    voice: "af_sky",
                });
            }
        }

        workercb(worker.current);

    }, [readable, worker, setStatus]);

    // Effect to set up the worker
    useEffect(() => {
        // Create the worker if it does not yet exist
        if (!worker.current) {
            worker.current = new Worker(new URL("./worker.js", import.meta.url), {
                type: "module",
            });
        }

        // Handle messages from the worker
        const handleMessage = (e: MessageEvent) => {

            switch (e.data.status) {
                case "progress":
                    // Handle progress updates for model loading
                    if (e.data.progress) {
                        const progressData = e.data.progress;
                        const fileKey = `${progressData.name}-${progressData.file}`;

                        setModelLoadingProgress(prev => ({
                            ...prev,
                            [fileKey]: {
                                name: progressData.name,
                                file: progressData.file,
                                progress: progressData.progress,
                                loaded: progressData.loaded,
                                total: progressData.total,
                                status: progressData.status
                            }
                        }));

                        // If this file is done, check if all files are done
                        if (progressData.status === "done") {
                            // Check if all files are done
                            setTimeout(() => {
                                setModelLoadingProgress(prev => {
                                    const allDone = Object.values(prev).every(file => file.status === "done");
                                    if (allDone) {
                                        setModelLoadingComplete(true);
                                    }
                                    return prev;
                                });
                            }, 100);
                        }

                        setIsModelLoading(true);
                    }
                    break;
                case "device":
                    setIsModelLoading(true);
                    setModelLoadingComplete(false);

                    showToast({
                        message: `Using ${e.data.device} with ${e.data.dtype} precision.`,
                        type: "info",
                        duration: 5000,
                    });

                    break;
                case "ready":
                    setStatus("ready");
                    setVoices(e.data.voices);
                    setIsModelLoading(false);
                    setModelLoadingComplete(true);
                    break;
                case "error":
                    console.error("Worker error:", e);
                    setError(e.data.data);
                    showToast({
                        message: e.data.data,
                        type: "error",
                        duration: 5000,
                    });

                    break;
                case "complete":
                    const { audio, text, chunkIndex, totalChunks } = e.data;

                    const newChunk = {
                        text,
                        src: audio,
                        id: Date.now(),
                        index: chunkIndex || 0,
                        total: totalChunks || 1
                    };

                    console.log(`Received chunk: "${text.substring(0, 30)}..."`);

                    // Add the new chunk to our array, but check for duplicates first
                    setAudioChunks(prev => {
                        // Check if this chunk is a duplicate (same text content)
                        const isDuplicate = prev.some(chunk =>
                            chunk.text === newChunk.text ||
                            (chunk.text && newChunk.text &&
                                chunk.text.trim().substring(0, 50) === newChunk.text.trim().substring(0, 50))
                        );

                        if (isDuplicate) {
                            console.log(`Duplicate chunk detected in state, ignoring: "${newChunk.text.substring(0, 30)}..."`);
                            return prev; // Return unchanged array
                        }

                        // Keep all chunks without limiting
                        let updatedPrev = [...prev];

                        const newChunks = [...updatedPrev, newChunk];

                        console.log(`New chunk added to queue, currentChunkIndex: ${currentChunkIndex}, newChunks.length: ${newChunks.length}`);

                        // If we're not currently processing a chunk
                        if (!processingChunkRef.current) {
                            // Check if the player is paused
                            const isPausedState = isPausedRef.current;

                            // If this is the first chunk and we're not playing anything yet, and not paused, start playing
                            if ((updatedPrev.length === 0 && currentChunkIndexRef.current === -1) ||
                                (currentChunkIndexRef.current === -1 && !isPlayingRef.current && !isPausedState)) {
                                console.log(`New chunk added to queue, starting playback`);

                                // Use setTimeout to ensure state update completes first
                                setTimeout(() => {
                                    // Double-check that we're still not playing and not paused
                                    if (currentChunkIndexRef.current === -1 && !isPlayingRef.current && !processingChunkRef.current && !isPausedState) {
                                        console.log(`Setting currentChunkIndex to 0`);
                                        setCurrentChunkIndex(0);
                                    } else {
                                        console.log(`State changed or player is paused, not starting playback`);
                                    }
                                }, 300); // Increased timeout for better reliability
                            }
                            // If we're already at the end of the current chunks and playing (not paused), move to the next chunk
                            else if (isPlayingRef.current && !isPausedState &&
                                currentChunkIndexRef.current === updatedPrev.length - 1 &&
                                audioRef.current?.ended) {
                                console.log(`Current chunk ended, moving to next chunk`);

                                // Use setTimeout to ensure state update completes first
                                setTimeout(() => {
                                    // Double-check that we're still at the end and not paused
                                    if (currentChunkIndexRef.current === updatedPrev.length - 1 &&
                                        !processingChunkRef.current && !isPausedState) {
                                        console.log(`Setting currentChunkIndex to ${updatedPrev.length}`);
                                        setCurrentChunkIndex(updatedPrev.length);
                                    } else {
                                        console.log(`State changed or player is paused, not moving to next chunk`);
                                    }
                                }, 300); // Increased timeout for better reliability
                            }
                            // Otherwise, just add the chunk to the queue without changing playback
                            else {
                                console.log(`New chunk added to queue, continuing current playback (index: ${currentChunkIndexRef.current}, playing: ${isPlayingRef.current}, paused: ${isPausedState})`);
                            }
                        } else {
                            console.log(`Currently processing a chunk, not changing playback state`);
                        }

                        return newChunks;
                    });

                    setStatus("ready");
                    break;
            }
        };

        // Handle errors from the worker
        const handleError = (e: ErrorEvent) => {
            console.error("Worker error:", e);
            setError(e.message);
        };

        // Add event listeners
        worker.current.addEventListener("message", handleMessage);
        worker.current.addEventListener("error", handleError);

        // Cleanup function
        return () => {
            if (worker.current) {
                worker.current.removeEventListener("message", handleMessage);
                worker.current.removeEventListener("error", handleError);
            }
        };
    }, []);

    // Effect to initialize model loading state when player becomes visible
    useEffect(() => {
        if (isVisible && !modelLoadingComplete && !isModelLoading) {
            console.log(`Player became visible, initializing model loading state`);
            setIsModelLoading(true); // Start in loading state until we know the model status
        }
    }, [isVisible, modelLoadingComplete, isModelLoading]);

    // Effect to clean up blob URLs when the component unmounts or when the player is closed
    useEffect(() => {
        // Only run cleanup when the player becomes invisible (closed)
        if (!isVisible) {
            // Clean up function to revoke all blob URLs when the component unmounts
            const cleanupBlobUrls = () => {
                console.log(`Cleaning up ${audioChunks.length} blob URLs`);
                audioChunks.forEach(chunk => {
                    if (chunk.src && chunk.src.startsWith('blob:')) {
                        try {
                            URL.revokeObjectURL(chunk.src);
                            console.log(`Revoked URL for chunk: ${chunk.id}`);
                        } catch (e) {
                            console.error(`Error revoking URL: ${e}`);
                        }
                    }
                });
            };

            // If the player is closed, clean up the blob URLs
            cleanupBlobUrls();
        }
    }, [isVisible, audioChunks]);

    // Effect to sync state with refs
    useEffect(() => {
        audioChunksRef.current = audioChunks;
    }, [audioChunks]);

    useEffect(() => {
        currentChunkIndexRef.current = currentChunkIndex;
    }, [currentChunkIndex]);

    useEffect(() => {
        isPlayingRef.current = isPlaying;
    }, [isPlaying]);

    useEffect(() => {
        isPausedRef.current = isPaused;
    }, [isPaused]);

    // Effect to handle playing audio when currentChunkIndex changes
    useEffect(() => {
        if (currentChunkIndex === -1 || !audioChunks.length) return;

        const currentChunk = audioChunks[currentChunkIndex];
        if (!currentChunk) return;

        // Prevent processing the same chunk multiple times
        if (processingChunkRef.current) {
            console.log(`Already processing a chunk, will wait`);
            return;
        }

        processingChunkRef.current = true;

        const playChunk = async () => {
            if (!audioRef.current) {
                processingChunkRef.current = false;
                return;
            }

            try {
                // Check if we're already playing this chunk
                if (audioRef.current.src === currentChunk.src && !audioRef.current.paused) {
                    processingChunkRef.current = false;
                    return;
                }

                // Reset the audio element
                audioRef.current.pause();
                audioRef.current.currentTime = 0;

                // Set the new source
                audioRef.current.src = currentChunk.src;

                // Load and play
                audioRef.current.load();

                // Wait for the audio to be loaded
                await new Promise<void>((resolve) => {
                    if (!audioRef.current) {
                        resolve();
                        return;
                    }

                    const handleCanPlay = () => {
                        audioRef.current?.removeEventListener('canplay', handleCanPlay);
                        resolve();
                    };

                    audioRef.current.addEventListener('canplay', handleCanPlay);

                    // Also resolve after a timeout in case the canplay event doesn't fire
                    setTimeout(resolve, 300);
                });

                // Start playing only if we're not paused
                if (audioRef.current && !isPaused) {
                    await audioRef.current.play();
                    setIsPlaying(true);
                    setIsPaused(false);
                } else if (isPaused) {
                    // If we're paused, just load the audio but don't play it
                    console.log('Audio loaded but not playing because player is paused');
                    setIsPlaying(false);
                    setIsPaused(true);
                }
            } catch (err) {
                console.error("Error playing audio:", err);

                // If there was an error, try to move to the next chunk if available
                if (currentChunkIndex < audioChunks.length - 1) {
                    setTimeout(() => {
                        setCurrentChunkIndex(prev => prev + 1);
                    }, 100);
                }
            } finally {
                // Always reset the processing flag
                setTimeout(() => {
                    processingChunkRef.current = false;
                }, 200);
            }
        };

        playChunk();
    }, [currentChunkIndex, audioChunks]);

    // Handle audio ended event
    useEffect(() => {
        const audio = audioRef.current;
        if (!audio) return;

        const handleEnded = () => {
            // If we're already processing a chunk, don't handle the ended event
            if (processingChunkRef.current) {
                console.log(`Already processing a chunk, ignoring ended event`);
                return;
            }

            // Set processing flag to prevent multiple ended events from being processed
            processingChunkRef.current = true;

            console.log(`Audio ended event for chunk ${currentChunkIndexRef.current + 1}/${audioChunksRef.current.length}`);

            try {
                // Create a local variable to track the current index to avoid race conditions
                const currentIndex = currentChunkIndexRef.current;

                // Move to the next chunk if available
                if (currentIndex < audioChunksRef.current.length - 1) {
                    console.log(`Moving to next chunk ${currentIndex + 2}/${audioChunksRef.current.length}`);

                    // Use setTimeout to ensure state updates properly
                    setTimeout(() => {
                        // Double-check that we haven't already moved to the next chunk
                        if (currentChunkIndexRef.current === currentIndex) {
                            console.log(`Setting currentChunkIndex to ${currentIndex + 1}`);
                            setCurrentChunkIndex(currentIndex + 1);
                        } else {
                            console.log(`Chunk index already changed, not updating`);
                        }
                    }, 300); // Increased timeout for better reliability
                } else {
                    console.log(`Reached the end of all chunks`);
                    setIsPlaying(false);
                }
            } catch (error) {
                console.error(`Error in handleEnded: ${error}`);
            } finally {
                // Reset processing flag after a delay
                setTimeout(() => {
                    processingChunkRef.current = false;
                }, 300);
            }
        };

        // Add timeupdate event to detect when we're near the end of a chunk
        const handleTimeUpdate = () => {
            if (!audio.paused && audio.duration > 0) {
                const timeRemaining = audio.duration - audio.currentTime;

                // If we're very close to the end (within 0.1 seconds) and there are more chunks
                if (timeRemaining < 0.1 && currentChunkIndex < audioChunks.length - 1) {
                    console.log(`Near end of chunk ${currentChunkIndex + 1}, preparing next chunk`);
                }
            }
        };

        audio.addEventListener('ended', handleEnded);
        audio.addEventListener('timeupdate', handleTimeUpdate);

        return () => {
            audio.removeEventListener('ended', handleEnded);
            audio.removeEventListener('timeupdate', handleTimeUpdate);
        };
    }, [currentChunkIndex, audioChunks]);

    // Handle play/pause button
    const handlePlayPause = () => {
        if (!audioRef.current) return;

        if (isPlaying) {
            // Pause the current audio
            audioRef.current.pause();
            setIsPlaying(false);
            setIsPaused(true);
        } else {
            // Resume or start playing
            if (currentChunkIndex === -1 && audioChunks.length > 0) {
                // Start from the beginning
                setCurrentChunkIndex(0);
            } else if (currentChunkIndex >= 0 && currentChunkIndex < audioChunks.length) {
                if (audioRef.current.paused) {
                    // Resume current audio
                    audioRef.current.play()
                        .then(() => {
                            setIsPlaying(true);
                            setIsPaused(false);
                        })
                        .catch(error => {
                            console.error("Error resuming playback:", error);
                            // If there was an error resuming, try to restart the chunk
                            if (audioChunks[currentChunkIndex] && audioRef.current) {
                                audioRef.current.src = audioChunks[currentChunkIndex].src;
                                audioRef.current.load();
                                audioRef.current.play()
                                    .then(() => {
                                        setIsPlaying(true);
                                        setIsPaused(false);
                                    })
                                    .catch(err2 => console.log(`Error restarting chunk: ${err2}`));
                            }
                        });
                }
            } else if (audioChunks.length > 0) {
                // If currentChunkIndex is out of bounds but we have chunks, start from the beginning
                setCurrentChunkIndex(0);
            }
        }
    };

    // Handle skip forward button (skip ahead 10 chunks if possible)
    const handleSkipForward = () => {
        // Only skip if there are at least 15 more chunks ahead
        if (audioChunks.length > currentChunkIndex + 15) {
            console.log(`Skipping forward 10 chunks from ${currentChunkIndex} to ${currentChunkIndex + 10}`);

            // Stop current playback
            if (audioRef.current && isPlaying) {
                audioRef.current.pause();
            }

            // Set processing flag to prevent race conditions
            processingChunkRef.current = true;

            // Update the current chunk index
            const newIndex = currentChunkIndex + 10;
            setCurrentChunkIndex(newIndex);

            // Reset processing flag after a delay
            setTimeout(() => {
                processingChunkRef.current = false;
            }, 100);
        } else {
            console.log(`Cannot skip forward: not enough chunks ahead (current: ${currentChunkIndex}, total: ${audioChunks.length})`);
        }
    };

    // Handle skip backward button (go back 10 chunks if possible)
    const handleSkipBackward = () => {
        // Only skip backward if we're at least at chunk 11
        if (currentChunkIndex >= 10) {
            console.log(`Skipping backward 10 chunks from ${currentChunkIndex} to ${currentChunkIndex - 10}`);

            // Stop current playback
            if (audioRef.current && isPlaying) {
                audioRef.current.pause();
            }

            // Set processing flag to prevent race conditions
            processingChunkRef.current = true;

            // Update the current chunk index
            const newIndex = currentChunkIndex - 10;
            setCurrentChunkIndex(newIndex);

            // Reset processing flag after a delay
            setTimeout(() => {
                processingChunkRef.current = false;
            }, 100);
        } else {
            console.log(`Cannot skip backward: not enough chunks behind (current: ${currentChunkIndex})`);
        }
    };



    // Loading UI component
    const LoadingUI = () => {
        const progressItems = Object.values(modelLoadingProgress);

        // Calculate total progress, handling potential NaN values
        let totalProgress = 0;
        if (progressItems.length > 0) {
            const validProgressItems = progressItems.filter(item =>
                !isNaN(item.progress) && item.progress !== undefined && item.progress !== null
            );

            if (validProgressItems.length > 0) {
                totalProgress = validProgressItems.reduce((sum, item) => sum + item.progress, 0) / validProgressItems.length;
            }
        }

        return (
            <div className="fixed bottom-0 left-0 right-0 transition-transform duration-300 ease-in-out transform h-[200px]">
                <div className="max-w-7xl mx-auto px-6 py-4 h-full flex flex-col justify-between">
                    {/* Top section with title and close button */}
                    <div className="flex justify-between items-start">
                        <div className="text-white">
                            <div className="text-sm font-medium">Loading model</div>
                            <div className="text-lg font-medium truncate">{title}</div>
                            <div className="text-xs text-white/70">
                                Please wait while we prepare your audio
                            </div>
                        </div>
                        <Button
                            variant="ghost"
                            className="text-white hover:bg-white/10 p-1 rounded-full"
                            onClick={onClose}
                            aria-label="Close player"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                        </Button>
                    </div>

                    {/* Loading progress section */}
                    <div className="flex flex-col items-center w-full">
                        {/* Progress bar */}
                        <div className="w-full max-w-xl">
                            <div className="flex justify-between items-center mb-2">
                                <span className="text-sm text-white">Downloading speech model...</span>
                                <span className="text-sm text-white">
                                    {isNaN(totalProgress) ? '0' : Math.round(totalProgress)}%
                                </span>
                            </div>
                            <div className="h-3 bg-white/30 rounded-full relative overflow-hidden">
                                {/* Show either a determinate or indeterminate progress bar */}
                                {progressItems.length > 0 && !isNaN(totalProgress) ? (
                                    // Determinate progress bar when we have valid progress data
                                    <div
                                        className="absolute left-0 top-0 h-full bg-white rounded-full transition-all duration-300"
                                        style={{ width: `${totalProgress}%` }}
                                    ></div>
                                ) : (
                                    // Indeterminate progress bar (animated) when we don't have progress data yet
                                    <div className="absolute left-0 top-0 h-full bg-white/50 rounded-full animate-pulse"></div>
                                )}
                            </div>


                        </div>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <div className='z-50'>
            {/* Hidden audio element */}
            <audio
                ref={audioRef}
                hidden
            />

            <div className="fixed bottom-0 h-[220px] bg-player-pattern">
                {/* Show loading UI or player based on loading state */}
                {isModelLoading && !modelLoadingComplete ? (
                    <LoadingUI />
                ) : (
                    <div className="max-w-7xl mx-auto px-6 py-32 h-full flex flex-col justify-between">
                        {/* Top section with title and close button */}
                        <div className="flex justify-between items-start">
                            <div className="text-white">
                                <div className="text-sm font-medium">Continue listening</div>
                                <div className="text-lg font-medium truncate">{title}</div>
                                <div className="text-xs text-white/70">
                                    {status === "running" ? "Processing audio..." :
                                        isPlaying ? `Playing chunk ${currentChunkIndex + 1} of ${audioChunks.length} (${audioChunks.length} total chunks)` :
                                            isPaused ? `Paused (chunk ${currentChunkIndex + 1} of ${audioChunks.length}) (${audioChunks.length} total chunks)` :
                                                audioChunks.length > 0 ? `Ready to play (${audioChunks.length} total chunks)` :
                                                    "Ready to play"}
                                </div>
                            </div>
                            <Button
                                variant="ghost"
                                className="text-white hover:bg-white/10 p-1 rounded-full"
                                onClick={onClose}
                                aria-label="Close player"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                    <line x1="18" y1="6" x2="6" y2="18"></line>
                                    <line x1="6" y1="6" x2="18" y2="18"></line>
                                </svg>
                            </Button>
                        </div>

                        {/* Controls section */}
                        <div className="flex flex-col items-center">
                            {/* Player controls */}
                            <div className="flex items-center justify-center space-x-6 mb-4">
                                <Button
                                    variant="ghost"
                                    className="rounded-full p-2 text-white hover:bg-white/10"
                                    aria-label="Show playlist"
                                >
                                    <svg className='w-8 h-8' width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M26.25 7.5V10H3.75V7.5H26.25ZM3.75 22.5H15V20H3.75V22.5ZM3.75 16.25H26.25V13.75H3.75V16.25Z" fill="white" fillOpacity="0.8"/>
                                    </svg>
                                </Button>

                                <Button
                                    variant="ghost"
                                    className="rounded-full p-2 text-white hover:bg-white/10"
                                    aria-label="Skip back 10 chunks"
                                    onClick={handleSkipBackward}
                                    disabled={currentChunkIndex < 10}
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                        <polyline points="1 4 1 10 7 10"></polyline>
                                        <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"></path>
                                    </svg>
                                </Button>

                                <Button
                                    onClick={audioChunks.length > 0 ? handlePlayPause : handleGenerate}
                                    variant="ghost"
                                    className="rounded-full bg-white text-[#493BC6] hover:bg-white/90 p-4"
                                    aria-label={isPlaying ? "Pause" : "Play"}
                                    disabled={status === "running"}
                                >
                                    {isPlaying ? (
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" stroke="none">
                                            <rect x="6" y="4" width="4" height="16" />
                                            <rect x="14" y="4" width="4" height="16" />
                                        </svg>
                                    ) : (
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" stroke="none">
                                            <polygon points="5 3 19 12 5 21 5 3"></polygon>
                                        </svg>
                                    )}
                                </Button>

                                <Button
                                    variant="ghost"
                                    className="rounded-full p-2 text-white hover:bg-white/10"
                                    aria-label="Skip forward 10 chunks"
                                    onClick={handleSkipForward}
                                    disabled={audioChunks.length <= currentChunkIndex + 15}
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                        <polyline points="23 4 23 10 17 10"></polyline>
                                        <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"></path>
                                    </svg>
                                </Button>

                                <Button
                                    variant="ghost"
                                    className="rounded-full p-2 text-white hover:bg-white/10"
                                    aria-label="Microphone"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                        <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                                        <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                                        <line x1="12" y1="19" x2="12" y2="23"></line>
                                        <line x1="8" y1="23" x2="16" y2="23"></line>
                                    </svg>
                                </Button>
                            </div>

                            {/* Progress bar */}
                            <div className="w-full max-w-xl flex items-center">
                                <span className="text-xs text-white mr-2">
                                    {currentChunkIndex >= 0 ? `Chunk ${currentChunkIndex + 1}` : '0'}
                                </span>
                                <div className="flex-1 h-1 bg-white/30 rounded-full relative">
                                    <div
                                        className="absolute left-0 top-0 h-full bg-white rounded-full"
                                        style={{
                                            width: audioChunks.length > 0
                                                ? `${((currentChunkIndex + 1) / audioChunks.length) * 100}%`
                                                : '0%'
                                        }}
                                    ></div>
                                    <div
                                        className="absolute h-3 w-3 bg-white rounded-full -top-1 transform -translate-x-1/2"
                                        style={{
                                            left: audioChunks.length > 0
                                                ? `${((currentChunkIndex + 1) / audioChunks.length) * 100}%`
                                                : '0%'
                                        }}
                                    ></div>
                                </div>
                                <span className="text-xs text-white ml-2">
                                    {audioChunks.length > 0 ? `${audioChunks.length} total` : '0'}
                                </span>
                            </div>

                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default Player;
