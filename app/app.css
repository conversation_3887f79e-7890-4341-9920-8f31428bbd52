
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import 'tailwindcss';

html, body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

@theme {

    --font-sans: 'Inter';
    --font-mono: 'Inter';
    --font-display: 'Inter';
    --default-font-family: var(--font-sans);

    --color-readable-navy: #172335;
    --color-readable-deep-navy: #1a2b47;
    --color-readable-light-blue: #c8e1ff;
    --color-readable-bg-blue: #e6f0ff;
    --color-readable-purple: #9b87f5;
    --color-readable-dark-purple: #7E69AB;
    --color-readable-gradient-start: #413394;
    --color-readable-gradient-end: #7E69AB;
    --color-readable-darker-purple: #664BD8;
    --color-readable-darkest-purple: #363076;

    --background: 210 100% 97%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 252 84% 74%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222 47% 11%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215 16% 47%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222 47% 11%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 252 84% 74%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5% 26%;
    --sidebar-primary: 240 6% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 5% 96%;
    --sidebar-accent-foreground: 240 6% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217 91% 60%;
    --color-border: 'hsl(var(--border))';
    --color-input: 'hsl(var(--input))';
    --color-ring: 'hsl(var(--ring))';
    --color-background: 'hsl(var(--background))';
    --color-foreground: 'hsl(var(--foreground))';
    --color-primary: 'hsl(var(--primary))';
    --color-primary-foreground: 'hsl(var(--primary-foreground))';
    --color-secondary: 'hsl(var(--secondary))';
    --color-secondary-foreground: 'hsl(var(--secondary-foreground))';
    --color-destructive: 'hsl(var(--destructive))';
    --color-destructive-foreground: 'hsl(var(--destructive-foreground))';
    --color-muted: 'hsl(var(--muted))';
    --color-muted-foreground: 'hsl(var(--muted-foreground))';
    --color-accent: 'hsl(var(--accent))';
    --color-accent-foreground: 'hsl(var(--accent-foreground))';
    --color-popover: 'hsl(var(--popover))';
    --color-popover-foreground: 'hsl(var(--popover-foreground))';
    --color-card: 'hsl(var(--card))';
    --color-card-foreground: 'hsl(var(--card-foreground))';
    --color-sidebar: 'hsl(var(--sidebar-background))';
    --color-sidebar-foreground: 'hsl(var(--sidebar-foreground))';
    --color-sidebar-primary: 'hsl(var(--sidebar-primary))';
    --color-tag-technology: '#FF6B6B';
    --color-tag: {
          'science': '#4ECDC4',
          'article': '#45B7D1',
          'picture': '#FFA62B',
          'text': '#A0A0A0',
          'cruiser': '#6C63FF',
        }
}
.bg-waves-01-pattern {
    background-image: url("/waves_001.svg");
    background-repeat: no-repeat;
}

.bg-waves-02-pattern {
    background-image: url("/waves.svg");
    background-repeat: no-repeat;
}

.bg-signup-pattern {
    background-image: url("/waves_002.svg");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: -100px 0px;
}

.bg-footer-pattern {
    background-image: url("/bg-footer.svg");
    background-repeat: no-repeat;
    background-position: -100px 0px
}

.bg-player-pattern {
    background-image: url("/player.svg");
    background-repeat: no-repeat;
    background-position: center;
}

.bg-player {
    background-image: url("/waves_player.svg");
    background-repeat: no-repeat;
    background-size: cover;
}


.tag {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.tag-technology {
  @apply bg-red-100 text-red-800;
}

.tag-science {
  @apply bg-green-100 text-green-800;
}

.tag-article {
  @apply bg-blue-100 text-blue-800;
}

.tag-picture {
  @apply bg-yellow-100 text-yellow-800;
}

.tag-text {
  @apply bg-gray-100 text-gray-800;
}

.tag-cruiser {
  @apply bg-indigo-100 text-indigo-800;
}

#root {
    width: 100%;
    margin: 0 auto;
    padding: 0;
  }

  .logo {
    height: 6em;
    padding: 1.5em;
    will-change: filter;
    transition: filter 300ms;
  }

  .card {
    padding: 2em;
  }

  .read-the-docs {
    color: #888;
  }

  .water-container {

    overflow: hidden;
    position: fixed;
    bottom: -200;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    line-height: 50px;
    animation: water-waves linear infinite;
  }


  .water-wave1 {
    position: absolute;
    top: 40%;
    left: -25%;
    background: #33cfff;
    opacity: 0.7;
    width: 200%;
    height: 200%;
    border-radius: 40%;
    animation: inherit;
    animation-duration: 5s;
  }
  .water-wave2 {
    position: absolute;
    top: 45%;
    left: -35%;
    background: #0eaffe;
    opacity: 0.5;
    width: 200%;
    height: 200%;
    border-radius: 35%;
    animation: inherit;
    animation-duration: 7s;
  }

  .water-wave3 {
    position: absolute;
    top: 50%;
    left: -35%;
    background: #0f7ae4;
    opacity: 0.3;
    width: 200%;
    height: 200%;
    border-radius: 33%;
    animation: inherit;
    animation-duration: 11s;
  }

  @keyframes water-waves {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
